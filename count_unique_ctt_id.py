#!/usr/bin/env python3
"""
统计csv/top1000.csv中ctt_id的唯一数量
"""

import pandas as pd
import sys
import os

def count_unique_ctt_id(csv_file_path):
    """
    统计CSV文件中ctt_id的唯一数量
    
    Args:
        csv_file_path (str): CSV文件路径
        
    Returns:
        int: ctt_id的唯一数量
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(csv_file_path):
            print(f"错误: 文件 {csv_file_path} 不存在")
            return None
            
        # 读取CSV文件
        print(f"正在读取文件: {csv_file_path}")
        df = pd.read_csv(csv_file_path)
        
        # 检查是否有ctt_id列
        if 'ctt_id' not in df.columns:
            print("错误: CSV文件中没有找到 'ctt_id' 列")
            print(f"可用的列: {list(df.columns)}")
            return None
            
        # 统计总行数
        total_rows = len(df)
        print(f"总行数: {total_rows}")
        
        # 统计ctt_id唯一数量
        unique_ctt_ids = df['ctt_id'].nunique()
        print(f"ctt_id唯一数量: {unique_ctt_ids}")
        
        # 统计重复数量
        duplicate_count = total_rows - unique_ctt_ids
        if duplicate_count > 0:
            print(f"重复的ctt_id数量: {duplicate_count}")
            
            # 显示重复的ctt_id
            duplicated_ids = df[df['ctt_id'].duplicated(keep=False)]['ctt_id'].unique()
            if len(duplicated_ids) > 0:
                print(f"重复的ctt_id列表 (前10个): {duplicated_ids[:10].tolist()}")
        else:
            print("没有重复的ctt_id")
            
        return unique_ctt_ids
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def main():
    """主函数"""
    csv_file_path = "csv/top1000.csv"
    
    print("=" * 50)
    print("CSV文件ctt_id唯一数量统计工具")
    print("=" * 50)
    
    unique_count = count_unique_ctt_id(csv_file_path)
    
    if unique_count is not None:
        print("=" * 50)
        print(f"结果: ctt_id唯一数量为 {unique_count}")
        print("=" * 50)
    else:
        print("统计失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
